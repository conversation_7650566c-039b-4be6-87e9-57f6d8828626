"use client";

import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
  useCallback,
} from "react";
import { AUTH_CONFIG } from "@/config/auth.config";
import { TenantUser } from "@workspace/database";
import { useLocalStorage } from "@/hooks/useLocalStorage";

export interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  role?: string;
  username?: string; // 添加用户名字段
  bio?: string; // 添加用户简介字段
}

export type TenantInfo = {
  id: string;
  name: string;
  type: string;
  role: string;
  tenantId: string;
  userId: string;
};

interface UserContextType {
  user: User | null;
  setUser: (user: User | null) => void;
  login: (user: User) => void;
  logout: () => Promise<void>;
  isAuthenticated: boolean;
  isLoading: boolean;
  tenants: TenantInfo[];
  setTenants: (tenants: TenantInfo[]) => void;
  currentTenant: TenantInfo | (TenantUser & { tenant: any }) | null;
  switchTenant: (tenantId: string) => Promise<TenantUser | null>;
  refreshToken: () => Promise<boolean>;
}

const UserContext = createContext<UserContextType | undefined>(undefined);

export function UserProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [currentTenant, setCurrentTenant] = useState<TenantInfo | null>(null);

  // 使用useLocalStorage hook管理tenants数据
  const [tenants, setTenants, removeTenants] = useLocalStorage<TenantInfo[]>(
    "user_tenants",
    [],
  );

  // 使用useLocalStorage hook管理currentTenant数据
  const [storedCurrentTenant, setStoredCurrentTenant, removeStoredCurrentTenant] = 
    useLocalStorage<TenantInfo | null>("current_tenant", null);

  // 由于token现在存储在httpOnly cookie中，我们不需要手动管理token
  // Cookie会自动随请求发送，服务端中间件会处理认证

  // 刷新访问令牌
  const refreshToken = useCallback(async (): Promise<boolean> => {
    try {
      const response = await fetch("/api/auth/refresh", {
        method: "POST",
        credentials: "include", // 包含cookies，服务端会自动更新cookie中的token
      });

      const result = await response.json();

      if (result.success && result.data) {
        // 服务端已经自动更新了cookie中的token，我们只需要更新本地状态

        // 更新用户信息
        if (result.data.user) {
          setUser(result.data.user);
        }

        // 更新租户信息
        if (result.data.tenants) {
          setTenants(result.data.tenants);
        }

        return true;
      } else {
        // 刷新失败，清除状态
        await logout();
        return false;
      }
    } catch (error) {
      console.error("Token refresh failed:", error);
      await logout();
      return false;
    }
  }, []);

  // 验证用户会话（使用cookie中的token）
  const validateSession = useCallback(async (): Promise<boolean> => {
    try {
      const response = await fetch("/api/auth/verify", {
        method: "POST",
        credentials: "include", // 使用cookie中的token
        headers: {
          "Content-Type": "application/json",
        },
      });

      const result = await response.json();

      if (result.success && result.user) {
        setUser(result.user);
        return true;
      } else {
        // Token无效，但不在这里刷新，避免死循环
        console.log("Session validation failed:", result.message);
        return false;
      }
    } catch (error) {
      console.error("Session validation failed:", error);
      return false;
    }
  }, []); // 移除 refreshToken 依赖

  // 初始化用户状态
  useEffect(() => {
    const initializeAuth = async () => {
      setIsLoading(true);

      // useLocalStorage hook已经自动从localStorage恢复tenants数据
      // 恢复 currentTenant 状态
      if (storedCurrentTenant) {
        setCurrentTenant(storedCurrentTenant);
      }

      // 尝试验证当前会话（使用cookie中的token）
      const isValid = await validateSession();
      if (!isValid) {
        // 如果验证和刷新都失败，清除状态
        setUser(null);
        setTenants([]);
        setCurrentTenant(null);
        removeStoredCurrentTenant();
      }

      setIsLoading(false);
    };

    initializeAuth();
  }, [validateSession, setTenants, storedCurrentTenant, removeStoredCurrentTenant]);

  // 设置token刷新定时器
  useEffect(() => {
    if (!user) return;

    // 每30分钟自动刷新token
    const refreshInterval = setInterval(() => {
      refreshToken();
    }, AUTH_CONFIG.TOKEN.AUTO_REFRESH_INTERVAL);

    return () => {
      clearInterval(refreshInterval);
    };
  }, [user, refreshToken]);

  const login = useCallback((userData: User) => {
    setUser(userData);
  }, []);

  const logout = useCallback(async () => {
    try {
      // 调用后端登出API，服务端会清除cookie中的token
      await fetch("/api/auth/logout", {
        method: "POST",
        credentials: "include", // 包含cookies
        headers: {
          "Content-Type": "application/json",
        },
      });
    } catch (error) {
      console.error("Logout API call failed:", error);
      // 即使API调用失败，也要清除本地状态
    } finally {
      // 清除本地状态
      setUser(null);
      setTenants([]);
      setCurrentTenant(null);

      // 清除localStorage中的tenants和currentTenant
      removeTenants();
      removeStoredCurrentTenant();

      // 跳转到登录页
      if (typeof window !== "undefined") {
        window.location.href = "/login";
      }
    }
  }, [removeTenants]);

  // 切换租户
  const switchTenant = useCallback(
    async (tenantId: string): Promise<TenantUser | null> => {
      try {
        const response = await fetch("/api/auth/switch-tenant", {
          method: "POST",
          credentials: "include", // 使用cookie中的token
          headers: {
            Host: `*.${window.location.host}`, // 确保请求头中包含正确的Host
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ tenantId }),
        });

        const result = await response.json();

        if (result.success && result.data) {
          // 服务端已经自动更新了cookie中的token

          // 如果有重定向URL，直接跳转到新域名
          // 暂时不需要重定向到子域名下，未来应该是学生和家长可能是需要访问子域名，并根据访问的子域名来反推租户,从而提供相应服务
          // if (result.redirectUrl) {
          //   if (typeof window !== "undefined") {
          //     window.location.href = result.redirectUrl;
          //   }
          //   return result.data.currentTenant as TenantUser;
          // }

          // 统一跳转到工作空间页面
          // router.push(`/workspace`);

          // 更新当前租户
          const newTenant = result.data.currentTenant;
          setCurrentTenant(newTenant);
          
          // 同步到本地存储
          setStoredCurrentTenant(newTenant);

          return newTenant as TenantUser;
        }

        return null;
      } catch (error) {
        console.error("Switch tenant failed:", error);
        return null;
      }
    },
    [],
  );

  const isAuthenticated = !!user;

  return (
    <UserContext.Provider
      value={{
        user,
        setUser,
        login,
        logout,
        isAuthenticated,
        isLoading,
        tenants,
        setTenants,
        currentTenant,
        switchTenant,
        refreshToken,
      }}
    >
      {children}
    </UserContext.Provider>
  );
}

export function useClientUser() {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error("useClientUser must be used within a UserProvider");
  }
  return context;
}
