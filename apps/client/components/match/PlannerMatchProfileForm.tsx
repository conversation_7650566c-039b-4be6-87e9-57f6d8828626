"use client";

import React, { useState, useEffect } from "react";
import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { toast } from "sonner";
import {
  Setting<PERSON>,
  Clock,
  DollarSign,
  Users,
  BookOpen,
  CheckCircle,
  AlertCircle,
  Loader2,
} from "lucide-react";

import { But<PERSON> } from "@workspace/ui/components/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@workspace/ui/components/card";
import { Input } from "@workspace/ui/components/input";
import { Label } from "@workspace/ui/components/label";
import { Switch } from "@workspace/ui/components/switch";
import { Textarea } from "@workspace/ui/components/textarea";
import { Badge } from "@workspace/ui/components/badge";
import { Separator } from "@workspace/ui/components/separator";
import { Alert, AlertDescription } from "@workspace/ui/components/alert";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  Dialog<PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogTrigger,
} from "@workspace/ui/components/dialog";
import { Checkbox } from "@workspace/ui/components/checkbox";

import { apiClient } from "@/utils/api";
import {
  GRADE_OPTIONS,
  SUBJECT_OPTIONS,
  LOCATION_OPTIONS,
  SUBJECT_GROUPS,
  GRADE_GROUPS,
  LOCATION_GROUPS,
  CATEGORY_CONFIG,
} from "@/constants/match-constants";
import { RequirementCategory } from "@/types/match.types";

// 表单验证schema
const profileFormSchema = z.object({
  isAcceptingMatch: z.boolean(),
  maxConcurrent: z.number().min(1).max(50),
  preferredGrades: z.array(z.string()),
  preferredSubjects: z.array(z.string()),
  preferredLocations: z.array(z.string()),
  responseTime: z.number().min(1).max(168),
  basePrice: z.number().min(0).optional(),
  priceRange: z
    .object({
      min: z.number().min(0).optional(),
      max: z.number().min(0).optional(),
    })
    .optional(),
  expertise: z
    .object({
      categories: z.array(z.nativeEnum(RequirementCategory)),
      description: z.string().optional(),
      certifications: z.array(z.string()),
    })
    .optional(),
  availability: z
    .object({
      timeSlots: z.array(z.string()),
      workingDays: z.array(z.string()),
      timezone: z.string().optional(),
    })
    .optional(),
});

export type ProfileFormData = z.infer<typeof profileFormSchema>;

interface PlannerMatchProfileFormProps {
  plannerId?: string;
  mode?: "full" | "compact" | "confirm";
  onSave?: (data: ProfileFormData) => void;
  onCancel?: () => void;
  className?: string;
}

// 时间段选项
const TIME_SLOTS = [
  { value: "morning", label: "上午 (9:00-12:00)" },
  { value: "afternoon", label: "下午 (13:00-17:00)" },
  { value: "evening", label: "晚上 (18:00-21:00)" },
  { value: "night", label: "深夜 (21:00-23:00)" },
];

// 工作日选项
const WORKING_DAYS = [
  { value: "monday", label: "周一" },
  { value: "tuesday", label: "周二" },
  { value: "wednesday", label: "周三" },
  { value: "thursday", label: "周四" },
  { value: "friday", label: "周五" },
  { value: "saturday", label: "周六" },
  { value: "sunday", label: "周日" },
];

export function PlannerMatchProfileForm({
  plannerId,
  mode = "full",
  onSave,
  onCancel,
  className,
}: PlannerMatchProfileFormProps) {
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [initialData, setInitialData] = useState<ProfileFormData | null>(null);

  const {
    control,
    handleSubmit,
    setValue,
    watch,
    formState: { errors, isDirty },
    reset,
  } = useForm<ProfileFormData>({
    resolver: zodResolver(profileFormSchema),
    defaultValues: {
      isAcceptingMatch: true,
      maxConcurrent: 10,
      preferredGrades: [],
      preferredSubjects: [],
      preferredLocations: [],
      responseTime: 24,
      basePrice: undefined,
      priceRange: { min: undefined, max: undefined },
      expertise: {
        categories: [],
        description: "",
        certifications: [],
      },
      availability: {
        timeSlots: [],
        workingDays: [],
        timezone: "Asia/Shanghai",
      },
    },
  });

  // 监听表单变化
  const watchedValues = watch();
  const isAcceptingMatch = watch("isAcceptingMatch");

  // 加载规划师配置
  useEffect(() => {
    if (plannerId) {
      fetchPlannerProfile();
    }
  }, [plannerId]);

  const fetchPlannerProfile = async () => {
    if (!plannerId) return;

    try {
      setLoading(true);
      const result = await apiClient.request(
        `/api/match/planner/${plannerId}/profile`,
      );

      if (result.success && result.data) {
        const profileData = result.data;

        // 转换数据格式
        const formData: ProfileFormData = {
          isAcceptingMatch: profileData.isAcceptingMatch ?? true,
          maxConcurrent: profileData.maxConcurrent ?? 10,
          preferredGrades: profileData.preferredGrades || [],
          preferredSubjects: profileData.preferredSubjects || [],
          preferredLocations: profileData.preferredLocations || [],
          responseTime: profileData.responseTime ?? 24,
          basePrice: profileData.basePrice,
          priceRange: profileData.priceRange || {
            min: undefined,
            max: undefined,
          },
          expertise: profileData.expertise || {
            categories: [],
            description: "",
            certifications: [],
          },
          availability: profileData.availability || {
            timeSlots: [],
            workingDays: [],
            timezone: "Asia/Shanghai",
          },
        };

        setInitialData(formData);
        reset(formData);
      }
    } catch (error) {
      toast.error("加载配置失败");
    } finally {
      setLoading(false);
    }
  };

  const onSubmit = async (data: ProfileFormData) => {
    if (!plannerId) {
      onSave?.(data);
      return;
    }

    try {
      setSaving(true);
      const result = await apiClient.request(
        `/api/match/planner/${plannerId}/profile`,
        {
          method: "PUT",
          body: JSON.stringify(data),
        },
      );

      if (result.success) {
        toast.success("配置保存成功");
        onSave?.(data);
      } else {
        toast.error(result.message || "保存失败");
      }
    } catch (error) {
      toast.error("保存失败，请重试");
    } finally {
      setSaving(false);
    }
  };

  const handleReset = () => {
    if (initialData) {
      reset(initialData);
    }
  };

  // 多选组件
  const MultiSelectField = ({
    label,
    value,
    onChange,
    options,
    groups,
    placeholder = "请选择",
    disabled = false,
  }: {
    label: string;
    value: string[];
    onChange: (value: string[]) => void;
    options: { value: string; label: string; group?: string }[];
    groups?: { label: string; options: any[] }[];
    placeholder?: string;
    disabled?: boolean;
  }) => {
    const [isOpen, setIsOpen] = useState(false);
    const [tempValue, setTempValue] = useState<string[]>([]);

    // 当弹窗打开时，初始化临时值
    const handleOpenChange = (open: boolean) => {
      if (open) {
        setTempValue([...value]);
      }
      setIsOpen(open);
    };

    const handleToggle = (optionValue: string) => {
      const newValue = tempValue.includes(optionValue)
        ? tempValue.filter((v) => v !== optionValue)
        : [...tempValue, optionValue];
      setTempValue(newValue);
    };

    const handleConfirm = () => {
      onChange(tempValue);
      setIsOpen(false);
    };

    const handleCancel = () => {
      setTempValue([...value]);
      setIsOpen(false);
    };

    return (
      <div className="space-y-2">
        <Label>{label}</Label>
        <Dialog open={isOpen} onOpenChange={handleOpenChange}>
          <DialogTrigger asChild>
            <Button
              variant="outline"
              className="w-full justify-start"
              disabled={disabled}
            >
              {value.length > 0 ? `已选择 ${value.length} 项` : placeholder}
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>选择{label}</DialogTitle>
              <DialogDescription>可以多选，点击确认保存选择</DialogDescription>
            </DialogHeader>
            <div className="space-y-4 max-h-96 overflow-y-auto">
              {groups
                ? groups.map((group) => (
                    <div key={group.label} className="space-y-2">
                      <Label className="text-sm font-medium">
                        {group.label}
                      </Label>
                      <div className="space-y-2">
                        {group.options.map((option) => (
                          <div
                            key={option}
                            className="flex items-center space-x-2"
                          >
                            <Checkbox
                              id={option}
                              checked={tempValue.includes(option)}
                              onCheckedChange={() => handleToggle(option)}
                            />
                            <Label htmlFor={option} className="text-sm">
                              {options.find((opt) => opt.value === option)
                                ?.label || option}
                            </Label>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))
                : options.map((option) => (
                    <div
                      key={option.value}
                      className="flex items-center space-x-2"
                    >
                      <Checkbox
                        id={option.value}
                        checked={tempValue.includes(option.value)}
                        onCheckedChange={() => handleToggle(option.value)}
                      />
                      <Label htmlFor={option.value} className="text-sm">
                        {option.label}
                      </Label>
                    </div>
                  ))}
            </div>
            <div className="flex gap-2 pt-4">
              <Button onClick={handleConfirm} className="flex-1">
                确认
              </Button>
              <Button onClick={handleCancel} variant="outline" className="flex-1">
                取消
              </Button>
            </div>
          </DialogContent>
        </Dialog>
        {value.length > 0 && (
          <div className="flex flex-wrap gap-1">
            {value.map((item) => (
              <Badge key={item} variant="secondary">
                {options.find((opt) => opt.value === item)?.label || item}
              </Badge>
            ))}
          </div>
        )}
      </div>
    );
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="py-8">
          <div className="flex items-center justify-center space-x-2">
            <Loader2 className="h-4 w-4 animate-spin" />
            <span>加载配置中...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Settings className="h-5 w-5" />
          规划师匹配配置
        </CardTitle>
        {!isAcceptingMatch && (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              您当前未开启匹配接单功能，开启后才能接收匹配请求。
            </AlertDescription>
          </Alert>
        )}
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* 基础设置 */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>接收匹配请求</Label>
                <p className="text-sm text-muted-foreground">
                  开启后您将接收到匹配请求通知
                </p>
              </div>
              <Controller
                name="isAcceptingMatch"
                control={control}
                render={({ field }) => (
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                )}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>最大并发数量</Label>
                <Controller
                  name="maxConcurrent"
                  control={control}
                  render={({ field }) => (
                    <Input
                      type="number"
                      min="1"
                      max="50"
                      {...field}
                      onChange={(e) => field.onChange(parseInt(e.target.value))}
                      disabled={!isAcceptingMatch}
                    />
                  )}
                />
                <p className="text-xs text-muted-foreground">
                  同时处理的学生数量上限
                </p>
              </div>

              <div className="space-y-2">
                <Label>响应时间 (小时)</Label>
                <Controller
                  name="responseTime"
                  control={control}
                  render={({ field }) => (
                    <Input
                      type="number"
                      min="1"
                      max="168"
                      {...field}
                      onChange={(e) => field.onChange(parseInt(e.target.value))}
                      disabled={!isAcceptingMatch}
                    />
                  )}
                />
                <p className="text-xs text-muted-foreground">承诺的响应时间</p>
              </div>
            </div>
          </div>

          {mode === "full" && (
            <>
              <Separator />

              {/* 偏好设置 */}
              <div className="space-y-4">
                <h4 className="font-medium flex items-center gap-2">
                  <Users className="h-4 w-4" />
                  偏好设置
                </h4>

                <Controller
                  name="preferredGrades"
                  control={control}
                  render={({ field }) => (
                    <MultiSelectField
                      label="偏好年级"
                      value={field.value}
                      onChange={field.onChange}
                      options={GRADE_OPTIONS}
                      groups={GRADE_GROUPS}
                      disabled={!isAcceptingMatch}
                    />
                  )}
                />

                <Controller
                  name="preferredSubjects"
                  control={control}
                  render={({ field }) => (
                    <MultiSelectField
                      label="偏好科目"
                      value={field.value}
                      onChange={field.onChange}
                      options={SUBJECT_OPTIONS}
                      groups={SUBJECT_GROUPS}
                      disabled={!isAcceptingMatch}
                    />
                  )}
                />

                <Controller
                  name="preferredLocations"
                  control={control}
                  render={({ field }) => (
                    <MultiSelectField
                      label="偏好地区"
                      value={field.value}
                      onChange={field.onChange}
                      options={LOCATION_OPTIONS}
                      groups={LOCATION_GROUPS}
                      disabled={!isAcceptingMatch}
                    />
                  )}
                />
              </div>

              <Separator />

              {/* 价格设置 */}
              <div className="space-y-4">
                <h4 className="font-medium flex items-center gap-2">
                  <DollarSign className="h-4 w-4" />
                  价格设置
                </h4>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label>基础价格</Label>
                    <Controller
                      name="basePrice"
                      control={control}
                      render={({ field }) => (
                        <Input
                          type="number"
                          min="0"
                          step="0.01"
                          placeholder="0.00"
                          {...field}
                          onChange={(e) =>
                            field.onChange(
                              parseFloat(e.target.value) || undefined,
                            )
                          }
                          disabled={!isAcceptingMatch}
                        />
                      )}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>价格区间 - 最低</Label>
                    <Controller
                      name="priceRange.min"
                      control={control}
                      render={({ field }) => (
                        <Input
                          type="number"
                          min="0"
                          step="0.01"
                          placeholder="0.00"
                          {...field}
                          onChange={(e) =>
                            field.onChange(
                              parseFloat(e.target.value) || undefined,
                            )
                          }
                          disabled={!isAcceptingMatch}
                        />
                      )}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>价格区间 - 最高</Label>
                    <Controller
                      name="priceRange.max"
                      control={control}
                      render={({ field }) => (
                        <Input
                          type="number"
                          min="0"
                          step="0.01"
                          placeholder="0.00"
                          {...field}
                          onChange={(e) =>
                            field.onChange(
                              parseFloat(e.target.value) || undefined,
                            )
                          }
                          disabled={!isAcceptingMatch}
                        />
                      )}
                    />
                  </div>
                </div>
              </div>

              <Separator />

              {/* 专长设置 */}
              <div className="space-y-4">
                <h4 className="font-medium flex items-center gap-2">
                  <BookOpen className="h-4 w-4" />
                  专长设置
                </h4>

                <div className="space-y-2">
                  <Label>擅长服务分类</Label>
                  <Controller
                    name="expertise.categories"
                    control={control}
                    render={({ field }) => (
                      <MultiSelectField
                        label="服务分类"
                        value={field.value}
                        onChange={field.onChange}
                        options={Object.entries(CATEGORY_CONFIG).map(
                          ([key, config]) => ({
                            value: key,
                            label: config.label,
                          }),
                        )}
                        disabled={!isAcceptingMatch}
                      />
                    )}
                  />
                </div>

                <div className="space-y-2">
                  <Label>专长描述</Label>
                  <Controller
                    name="expertise.description"
                    control={control}
                    render={({ field }) => (
                      <Textarea
                        placeholder="请描述您的专长和特色..."
                        rows={3}
                        {...field}
                        disabled={!isAcceptingMatch}
                      />
                    )}
                  />
                </div>
              </div>

              <Separator />

              {/* 时间设置 */}
              <div className="space-y-4">
                <h4 className="font-medium flex items-center gap-2">
                  <Clock className="h-4 w-4" />
                  时间设置
                </h4>

                <Controller
                  name="availability.timeSlots"
                  control={control}
                  render={({ field }) => (
                    <MultiSelectField
                      label="可用时段"
                      value={field.value}
                      onChange={field.onChange}
                      options={TIME_SLOTS}
                      disabled={!isAcceptingMatch}
                    />
                  )}
                />

                <Controller
                  name="availability.workingDays"
                  control={control}
                  render={({ field }) => (
                    <MultiSelectField
                      label="工作日"
                      value={field.value}
                      onChange={field.onChange}
                      options={WORKING_DAYS}
                      disabled={!isAcceptingMatch}
                    />
                  )}
                />
              </div>
            </>
          )}

          {/* 操作按钮 */}
          <div className="flex flex-col sm:flex-row gap-3 pt-4">
            <Button
              type="submit"
              disabled={saving || !isDirty}
              className="flex-1"
            >
              {saving ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  保存中...
                </>
              ) : (
                <>
                  <CheckCircle className="mr-2 h-4 w-4" />
                  保存配置
                </>
              )}
            </Button>

            {mode === "full" && (
              <Button
                type="button"
                variant="outline"
                onClick={handleReset}
                disabled={saving || !isDirty}
              >
                重置
              </Button>
            )}

            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={saving}
              >
                取消
              </Button>
            )}
          </div>
        </form>
      </CardContent>
    </Card>
  );
}

// 简化版配置弹窗
export function PlannerMatchProfileDialog({
  plannerId,
  children,
  onSave,
}: {
  plannerId: string;
  children: React.ReactNode;
  onSave?: (data: ProfileFormData) => void;
}) {
  const [open, setOpen] = useState(false);

  const handleSave = (data: ProfileFormData) => {
    onSave?.(data);
    setOpen(false);
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>匹配配置</DialogTitle>
          <DialogDescription>
            请确认您的匹配配置，确保能够接收到合适的匹配请求。
          </DialogDescription>
        </DialogHeader>
        <PlannerMatchProfileForm
          plannerId={plannerId}
          mode="compact"
          onSave={handleSave}
          onCancel={() => setOpen(false)}
        />
      </DialogContent>
    </Dialog>
  );
}
